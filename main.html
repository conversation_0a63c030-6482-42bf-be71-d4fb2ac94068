<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>प्रॉम्प्ट अनुकूलन रिपोर्ट</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            text-align: center;
            margin-bottom: 30px;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: white;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            border-right: 1px solid #eee;
        }

        .tab-button:last-child {
            border-right: none;
        }

        .tab-button.active {
            background: #3498db;
            color: white;
        }

        .tab-button:hover {
            background: #ecf0f1;
        }

        .tab-button.active:hover {
            background: #2980b9;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .section h3 {
            color: #34495e;
            margin: 20px 0 10px 0;
        }

        .principles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .principle-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .principle-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .prompt-card {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .prompt-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .prompt-header {
            background: #34495e;
            color: white;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .prompt-header:hover {
            background: #2c3e50;
        }

        .prompt-title {
            font-weight: bold;
            font-size: 1.1rem;
        }

        .expand-icon {
            transition: transform 0.3s ease;
        }

        .prompt-card.expanded .expand-icon {
            transform: rotate(180deg);
        }

        .prompt-content {
            display: none;
            padding: 20px;
        }

        .prompt-content.show {
            display: block;
        }

        .original-prompt, .optimized-prompt {
            margin: 15px 0;
        }

        .original-prompt h4, .optimized-prompt h4 {
            color: #e74c3c;
            margin-bottom: 10px;
        }

        .optimized-prompt h4 {
            color: #27ae60;
        }

        .prompt-text {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .changes-list {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .changes-list h4 {
            color: #2980b9;
            margin-bottom: 10px;
        }

        .changes-list ul {
            margin-left: 20px;
        }

        .changes-list li {
            margin: 5px 0;
        }

        .category-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .search-box {
            width: 100%;
            padding: 12px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            margin-bottom: 20px;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #3498db;
        }

        .highlight {
            background-color: yellow;
            padding: 2px 4px;
            border-radius: 3px;
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }

            .tab-button {
                border-right: none;
                border-bottom: 1px solid #eee;
            }

            .tab-button:last-child {
                border-bottom: none;
            }

            .container {
                padding: 10px;
            }

            .tab-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>एआई-जनरेटेड छवियों और वीडियो के लिए प्रॉम्प्ट अनुकूलन</h1>
            <p class="subtitle">विस्तृत रिपोर्ट और अनुकूलित प्रॉम्प्ट्स</p>
        </div>
    </header>

    <div class="container">
        <div class="nav-tabs">
            <button class="tab-button active" onclick="showTab('overview')">परिचय</button>
            <button class="tab-button" onclick="showTab('principles')">सिद्धांत</button>
            <button class="tab-button" onclick="showTab('analysis')">विश्लेषण</button>
            <button class="tab-button" onclick="showTab('prompts')">अनुकूलित प्रॉम्प्ट्स</button>
        </div>

        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="section">
                <h2>परिचय</h2>
                <p>यह रिपोर्ट एआई-आधारित छवि और वीडियो जनरेशन के लिए उपयोगकर्ता द्वारा प्रदान किए गए प्रॉम्प्ट्स के गहन विश्लेषण, त्रुटि सुधार और अनुकूलन पर केंद्रित है। इसका प्राथमिक उद्देश्य इन प्रॉम्प्ट्स को "पूरी तरह से अनुकूलित" करना है, यह सुनिश्चित करते हुए कि वे एआई मॉडल्स से उच्च-गुणवत्ता वाले, सटीक और रचनात्मक आउटपुट उत्पन्न करें।</p>

                <div class="category-stats">
                    <div class="stat-card">
                        <div class="stat-number">30</div>
                        <div class="stat-label">कुल प्रॉम्प्ट्स</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <div class="stat-label">मुख्य श्रेणियां</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">अनुकूलन दर</div>
                    </div>
                </div>

                <h3>मुख्य श्रेणियां</h3>
                <div class="principles-grid">
                    <div class="principle-card">
                        <h4>सामान्य अंतरंगता और भावनात्मक संबंध</h4>
                        <p>10 प्रॉम्प्ट्स - भावनात्मक गहराई और अंतरंग संबंधों पर केंद्रित</p>
                    </div>
                    <div class="principle-card">
                        <h4>आभूषण केंद्रित</h4>
                        <p>10 प्रॉम्प्ट्स - पारंपरिक आभूषणों और सजावट पर जोर</p>
                    </div>
                    <div class="principle-card">
                        <h4>स्पष्ट रूप से कामुक/यौन विषय</h4>
                        <p>5 प्रॉम्प्ट्स - कामुक और यौन संदर्भों के साथ</p>
                    </div>
                    <div class="principle-card">
                        <h4>भावनात्मक संकट और सांत्वना</h4>
                        <p>5 प्रॉम्प्ट्स - भावनात्मक समर्थन और सांत्वना के दृश्य</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Principles Tab -->
        <div id="principles" class="tab-content">
            <div class="section">
                <h2>एआई प्रॉम्प्ट इंजीनियरिंग के सिद्धांत</h2>

                <h3>प्रभावी प्रॉम्प्ट्स के मुख्य घटक</h3>
                <div class="principles-grid">
                    <div class="principle-card">
                        <h4>विषय (Subject)</h4>
                        <p>छवि का मुख्य फोकस - व्यक्ति, जानवर, या वस्तु</p>
                    </div>
                    <div class="principle-card">
                        <h4>क्रिया (Action)</h4>
                        <p>विषय क्या कर रहा है और कैसे कर रहा है</p>
                    </div>
                    <div class="principle-card">
                        <h4>वातावरण (Environment)</h4>
                        <p>स्थान, मौसम, भूभाग और पृष्ठभूमि</p>
                    </div>
                    <div class="principle-card">
                        <h4>माध्यम/शैली (Medium/Style)</h4>
                        <p>कला रूप या दृश्य शैली - फोटोरियलिस्टिक, तेल चित्रकला</p>
                    </div>
                    <div class="principle-card">
                        <h4>प्रकाश (Lighting)</h4>
                        <p>प्रकाश की गुणवत्ता, दिशा, रंग और स्रोत</p>
                    </div>
                    <div class="principle-card">
                        <h4>रंग (Color)</h4>
                        <p>रंग योजना, जीवंतता, या म्यूटेशन</p>
                    </div>
                    <div class="principle-card">
                        <h4>मनोदशा (Mood)</h4>
                        <p>छवि का समग्र भावनात्मक अनुभव</p>
                    </div>
                    <div class="principle-card">
                        <h4>संरचना/फ्रेमिंग</h4>
                        <p>कैमरा कोण, शॉट प्रकार, और बोकेह</p>
                    </div>
                </div>

                <h3>विशिष्टता और विवरण का महत्व</h3>
                <p>जितना अधिक विशिष्ट और विस्तृत प्रॉम्प्ट होगा, एआई मॉडल द्वारा वांछित परिणाम उत्पन्न करने की संभावना उतनी ही अधिक होगी। विशिष्टता का अर्थ केवल अधिक शब्द जोड़ना नहीं है, बल्कि एआई व्याख्या के लिए सबसे सटीक और प्रभावशाली शब्दों का चयन करना है।</p>

                <h3>तकनीकी पैरामीटर्स</h3>
                <div class="principle-card">
                    <h4>तकनीकी विवरण</h4>
                    <p>कैमरा लेंस (DSLR 85mm), रिज़ॉल्यूशन (8K UHD), बोकेह प्रभाव, HDR, सिनेमैटिक कलर ग्रेडिंग जैसे तकनीकी विवरण छवि की गुणवत्ता में काफी सुधार करते हैं।</p>
                </div>

                <div class="principle-card">
                    <h4>नकारात्मक प्रॉम्प्ट्स</h4>
                    <p>अवांछित तत्वों को हटाने के लिए महत्वपूर्ण हैं। ये एआई को उन सुविधाओं को उत्पन्न करने से रोकते हैं जो अवांछित हैं।</p>
                </div>
            </div>
        </div>

        <!-- Analysis Tab -->
        <div id="analysis" class="tab-content">
            <div class="section">
                <h2>मूल प्रॉम्प्ट्स का विश्लेषण</h2>

                <h3>सामान्य अवलोकन</h3>
                <p>दस्तावेज़ में कुल 30 प्रॉम्प्ट्स शामिल हैं, जिन्हें चार मुख्य श्रेणियों में वर्गीकृत किया गया है। प्रत्येक प्रॉम्प्ट एक 30-38 वर्षीय भारतीय स्त्रैण पुरुष और एक शक्तिशाली, मांसल भारतीय पुरुष के बीच एक अंतरंग दृश्य का वर्णन करता है।</p>

                <h3>शक्तियाँ</h3>
                <div class="principles-grid">
                    <div class="principle-card">
                        <h4>विस्तृत चरित्र विवरण</h4>
                        <p>प्रॉम्प्ट्स में "स्त्रैण पुरुष" की त्वचा, व्यवहार, बाल और चेहरे की विशेषताओं का विस्तृत वर्णन किया गया है।</p>
                    </div>
                    <div class="principle-card">
                        <h4>समृद्ध सेटिंग विवरण</h4>
                        <p>प्रत्येक प्रॉम्प्ट में वातावरण, प्रकाश व्यवस्था, सुगंध और ध्वनि का विस्तृत वर्णन है।</p>
                    </div>
                    <div class="principle-card">
                        <h4>तकनीकी कैमरा विवरण</h4>
                        <p>8K UHD, हाइपर-रियलिस्टिक, DSLR लेंस, बोकेह प्रभाव जैसे उच्च-गुणवत्ता वाले तकनीकी पैरामीटर्स का समावेश।</p>
                    </div>
                    <div class="principle-card">
                        <h4>भावनात्मक गहराई</h4>
                        <p>प्रॉम्प्ट्स में गहरे भावनात्मक बंधन, प्रेम, विश्वास और कामुकता पर जोर दिया गया है।</p>
                    </div>
                </div>

                <h3>सुधार के संभावित क्षेत्र</h3>
                <div class="principle-card">
                    <h4>संगति</h4>
                    <p>प्रॉम्प्ट्स के बीच विवरण के स्तर और संरचना में भिन्नता है। कुछ प्रॉम्प्ट्स में दूसरों की तुलना में अधिक तकनीकी या वर्णनात्मक विवरण हैं।</p>
                </div>

                <div class="principle-card">
                    <h4>विशिष्ट कीवर्ड्स</h4>
                    <p>बालों की बनावट, कपड़ों के विवरण, आभूषणों की बारीकियों और भावनात्मक अवस्थाओं के लिए अधिक लक्षित कीवर्ड्स जोड़ने का अवसर है।</p>
                </div>

                <div class="principle-card">
                    <h4>वीडियो अनुकूलन</h4>
                    <p>प्रॉम्प्ट्स में कैमरा गति, लौकिक तत्वों और समय के साथ क्रियाओं को परिभाषित करने के लिए स्पष्ट निर्देशों का अभाव है।</p>
                </div>
            </div>
        </div>

        <!-- Prompts Tab -->
        <div id="prompts" class="tab-content">
            <div class="section">
                <h2>अनुकूलित प्रॉम्प्ट्स</h2>
                <input type="text" class="search-box" placeholder="प्रॉम्प्ट्स में खोजें..." onkeyup="searchPrompts(this.value)">





                <h3>आभूषण केंद्रित</h3>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">1. पुस्तकालय में विद्वत्तापूर्ण जुड़ाव</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">35 वर्षीय स्त्रैण पुरुष और 50 वर्षीय मांसल पुरुष पुस्तकालय में बैठे हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">सिनेमैटिक फोटोग्राफी, अल्ट्रा-डिटेल्ड रेंडर, यथार्थवादी प्रकाश व्यवस्था, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। एक 35 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी और चीनी मिट्टी जैसी है, आंतरिक चमक से दमक रही है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें शांतिपूर्ण चिंतन में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं जिसमें हल्का गुलाब-गुलाबी रंगत है, जो गहरी भावनात्मक जुड़ाव और विद्वत्तापूर्ण मेल-मिलाप को दर्शाता है। उसके शानदार समृद्ध शाहबलूत भूरे रंग के बाल, कमर तक लंबे, आयतन के लिए कुशलता से लेयर्ड, एक भारी, स्त्रैण झरने की तरह स्टाइल किए हुए हैं, प्रत्येक लट रेशमी चमक बिखेर रही है, एक शानदार सोने के मोर के हेयरपिन से सजे हुए हैं जिसमें एक शानदार रूबी कैबोचोन जड़ा हुआ है। वह बेहतरीन रेशम के एक सुरुचिपूर्ण क्रीम रंग के सलवार सूट में सजे हुए हैं, जिसमें सूक्ष्म सोने के धागे की कढ़ाई (पैस्ले, संस्कृत प्रतीक, कमल के पैटर्न) और नाजुक सेक्विन के निशान हैं। मोती के गुच्छों के साथ पतला चांदी का दुपट्टा, पारंपरिक चांदी के झुमके, एक केंद्रीय चमकदार मोती के साथ नाजुक मांग टीका।

--neg विकृत, अस्पष्ट, खराब गुणवत्ता, असंगत, अतिरिक्त अंग, कटे हुए अंग, वाटरमार्क, टेक्स्ट, लोगो, खराब कला, खराब बनावट, अवास्तविक</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: रूबी कैबोचोन और मांग टीका का विस्तृत वर्णन</li>
                                <li>केश विन्यास का अधिक विस्तृत वर्णन</li>
                                <li>संवेदी अनुभव का विस्तार</li>
                                <li>तकनीकी मापदंडों में सुधार</li>
                            </ul>
                        </div>
                    </div>
                </div>



                <div class="prompt-card" data-category="intimacy">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">4. वैवाहिक प्रत्याशा</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष शादी के कमरे में अंतरंगता से लेटे हुए हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">ओवरहेड शॉट, नरम छाया, 8K UHD, सिनेमैटिक लाइटिंग, HDR, फोटोरियलिस्टिक, धीमी कैमरा रोटेशन। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और पॉलिश किए हुए हाथी दांत की तरह गर्मजोशी बिखेर रही है, आंतरिक शांति और प्रत्याशा से चमक रहा है। वह बेहतरीन रेशम के एक लुभावने शाही नीले अनारकली सूट में सजे हुए है, जिसमें जटिल सुनहरे धागे की कढ़ाई (Paisley, कमल के रूपांकन) और नाजुक दर्पण का काम है।

--neg विकृत, अस्पष्ट, खराब गुणवत्ता, असंगत, अतिरिक्त अंग, कटे हुए अंग, वाटरमार्क, टेक्स्ट, लोगो, खराब कला, खराब बनावट, अवास्तविक</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>वीडियो अनुकूलन: धीमी कैमरा रोटेशन जोड़ी गई</li>
                                <li>वस्त्र: लुभावने शाही नीले अनारकली सूट का विवरण</li>
                                <li>संवेदी विवरण का विस्तार</li>
                                <li>प्रकाश व्यवस्था में सुधार</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="intimacy">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">5. आधुनिक महल में कोमलता</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष आधुनिक महल में अंतरंगता से खड़े हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">वाइड शॉट, नरम प्रकाश, 8K UHD, फोटोरियलिस्टिक, DSLR 35mm लेंस, प्राकृतिक बोकेह प्रभाव, सिनेमैटिक लाइटिंग, धीमी कैमरा पैनिंग। वह बेहतरीन रेशमी शिफॉन के एक उत्कृष्ट नरम आड़ू रंग के बहते गाउन में सजे हुए है, जिसमें नाजुक फीते की कढ़ाई (फूलों के पैटर्न) और छोटे बीज मोती हैं। पतला सुनहरा शॉल।

--neg विकृत, अस्पष्ट, खराब गुणवत्ता, असंगत, अतिरिक्त अंग, कटे हुए अंग, वाटरमार्क, टेक्स्ट, लोगो, खराब कला, खराब बनावट, अवास्तविक</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>वीडियो अनुकूलन: धीमी कैमरा पैनिंग जोड़ी गई</li>
                                <li>वस्त्र: उत्कृष्ट नरम आड़ू रंग के गाउन का विवरण</li>
                                <li>तकनीकी पैरामीटर्स में सुधार</li>
                                <li>प्रकाश और बनावट पर जोर</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Remaining prompts from सामान्य अंतरंगता और भावनात्मक संबंध category -->
                <div class="prompt-card" data-category="intimacy">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">6. पारंपरिक शयनकक्ष में रोमांस</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष पारंपरिक शयनकक्ष में अंतरंगता से बैठे हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">मीडियम शॉट, गर्म प्रकाश, 8K UHD, फोटोरियलिस्टिक, सिनेमैटिक लाइटिंग, धीमी ज़ूम-आउट गति। पारंपरिक भारतीय शयनकक्ष में रोमांटिक माहौल के साथ अंतरंग दृश्य।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>वीडियो अनुकूलन: धीमी ज़ूम-आउट गति</li>
                                <li>पारंपरिक सेटिंग का विस्तार</li>
                                <li>रोमांटिक माहौल पर जोर</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="intimacy">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">7. गुलाब बगीचे में प्रेम</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष गुलाब बगीचे में खड़े हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">वाइड शॉट, प्राकृतिक प्रकाश, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। खिले हुए गुलाबों से भरे बगीचे में रोमांटिक दृश्य।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>प्राकृतिक सेटिंग का विस्तार</li>
                                <li>गुलाब बगीचे का विस्तृत वर्णन</li>
                                <li>प्राकृतिक प्रकाश का उपयोग</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="intimacy">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">8. चांदनी रात में मिलन</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष चांदनी रात में मिल रहे हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">नाइट शॉट, चांदनी प्रकाश, 8K UHD, फोटोरियलिस्टिक, सिनेमैटिक लाइटिंग, धीमी कैमरा ट्रैकिंग। चांदनी की रोशनी में रोमांटिक मुलाकात।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>चांदनी प्रकाश का प्रभावी उपयोग</li>
                                <li>रात्रिकालीन रोमांस का चित्रण</li>
                                <li>वीडियो अनुकूलन: धीमी कैमरा ट्रैकिंग</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="intimacy">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">9. सुबह की धूप में प्रेम</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष सुबह की धूप में एक साथ हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">गोल्डन आवर शॉट, प्राकृतिक प्रकाश, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। सुबह की सुनहरी धूप में प्रेमपूर्ण क्षण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>गोल्डन आवर प्रकाश का उपयोग</li>
                                <li>प्राकृतिक सुंदरता पर जोर</li>
                                <li>सुबह के माहौल का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="intimacy">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">10. शाम की शांति में एकता</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष शाम की शांति में एक साथ हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">सूर्यास्त शॉट, गर्म रंग, 8K UHD, फोटोरियलिस्टिक, सिनेमैटिक लाइटिंग, धीमी ज़ूम-इन गति। शाम की शांति में प्रेमपूर्ण एकता।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>सूर्यास्त के गर्म रंगों का उपयोग</li>
                                <li>शांतिपूर्ण माहौल का चित्रण</li>
                                <li>एकता की भावना पर जोर</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- आभूषण केंद्रित category - remaining prompts -->
                <h3>आभूषण केंद्रित - अतिरिक्त प्रॉम्प्ट्स</h3>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">2. किले में शाही गरिमा</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 38 वर्षीय मांसल पुरुष किले में खड़े हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">अति-यथार्थवादी सिनेमैटिक रेंडर, गहरी रंग योजना, 8K UHD, फोटोरियलिस्टिक, डायनामिक कैमरा ट्रैकिंग। शानदार नीलमणि से जड़ी एक उत्कृष्ट हेयर चेन और एक छोटे लटकते मोती के साथ पारंपरिक सोने की नथ के साथ सजे हुए।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: नीलमणि हेयर चेन और सोने की नथ</li>
                                <li>शाही गरिमा का चित्रण</li>
                                <li>किले की भव्यता का वर्णन</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">3. नदी तट पर युवा निर्दोषता</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">25 वर्षीय स्त्रैण पुरुष और 45 वर्षीय मांसल पुरुष नदी तट पर बैठे हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">फोटोरियलिस्टिक, प्राकृतिक प्रकाश, 8K विवरण, सिनेमैटिक कलर ग्रेडिंग, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। शानदार सोने का मोर हेयरपिन जिसमें शानदार रूबी कैबोचोन जड़ा हुआ है और छोटी घंटियों के साथ पारंपरिक चांदी की पायल।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: सोने का मोर हेयरपिन और चांदी की पायल</li>
                                <li>नदी तट की प्राकृतिक सुंदरता</li>
                                <li>युवा निर्दोषता का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">4. मंदिर में भक्तिपूर्ण श्रद्धा</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">27 वर्षीय स्त्रैण पुरुष और 40 वर्षीय मांसल पुरुष मंदिर में खड़े हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">सिनेमैटिक क्लोज-अप, सॉफ्ट फोकस, आध्यात्मिक वातावरण, 8K UHD, फोटोरियलिस्टिक, वॉल्यूमेट्रिक लाइटिंग, धीमी ज़ूम-इन गति। हीरे और छोटे मोतियों से सजी पतली चांदी की चेन और एक केंद्रीय रूबी लटकन के साथ शानदार पारंपरिक मांग टीका।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: हीरे की चेन और रूबी मांग टीका</li>
                                <li>मंदिर का आध्यात्मिक वातावरण</li>
                                <li>भक्तिपूर्ण श्रद्धा का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">5. जंगल में प्राकृतिक सामंजस्य</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">36 वर्षीय स्त्रैण पुरुष और 47 वर्षीय मांसल पुरुष जंगल में बैठे हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">सिनेमैटिक वाइड शॉट, सॉफ्ट डिफ्यूज्ड लाइटिंग, यथार्थवादी बनावट, 8K UHD, फोटोरियलिस्टिक, प्राकृतिक बोकेह प्रभाव, धीमी कैमरा पैनिंग। छोटे मोती और क्रिस्टल पिरोए गए चोटी और एक चमकदार मोती कमल पुष्प हेयर क्लिप।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: मोती क्रिस्टल चोटी और कमल हेयर क्लिप</li>
                                <li>जंगल का प्राकृतिक वातावरण</li>
                                <li>प्राकृतिक सामंजस्य का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">6. रेगिस्तान में रोमांटिक लालसा</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">33 वर्षीय स्त्रैण पुरुष और 49 वर्षीय मांसल पुरुष रेगिस्तान में खड़े हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">सिनेमैटिक वाइड शॉट, गोल्डन आवर लाइटिंग, यथार्थवादी रेगिस्तानी बनावट, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। शानदार नीलमणि से जड़ी एक उत्कृष्ट हेयर चेन और शानदार पारंपरिक सोने का चोकर हार।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: नीलमणि हेयर चेन और सोने का चोकर</li>
                                <li>रेगिस्तान की भव्यता</li>
                                <li>रोमांटिक लालसा का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">7. शहरी छत पर शहरी रोमांस</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">26 वर्षीय स्त्रैण पुरुष और 41 वर्षीय मांसल पुरुष शहरी छत पर खड़े हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">सिनेमैटिक नाइट शॉट, शहरी बोकेह प्रभाव, उच्च विवरण, 8K UHD, फोटोरियलिस्टिक, डायनामिक कैमरा ट्रैकिंग। एक शानदार रूबी कैबोचोन जड़ा हुआ है और समकालीन डिजाइनों और छोटी घंटियों के साथ पारंपरिक चांदी की पायल।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: रूबी कैबोचोन और समकालीन पायल</li>
                                <li>शहरी रात्रि का वातावरण</li>
                                <li>आधुनिक रोमांस का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">8. हिमालयी केबिन में शीतकालीन आराम</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">31 वर्षीय स्त्रैण पुरुष और 33 वर्षीय मांसल पुरुष हिमालयी केबिन में बैठे हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">अति-यथार्थवादी, आरामदायक प्रकाश, विस्तृत बनावट, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। एक उत्कृष्ट सोने के हेयरपिन से सजाया गया है जिसमें एक शानदार रूबी कैबोचोन जड़ा हुआ है और एक केंद्रीय पन्ने के लटकन के साथ शानदार पारंपरिक मांग टीका।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: सोने का हेयरपिन और पन्ने का मांग टीका</li>
                                <li>हिमालयी केबिन का आरामदायक वातावरण</li>
                                <li>शीतकालीन आराम का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">9. समुद्र तट पर तटीय स्थिरता</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">34 वर्षीय स्त्रैण पुरुष और 37 वर्षीय मांसल पुरुष समुद्र तट पर बैठे हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">सिनेमैटिक सूर्यास्त शॉट, नरम बोकेह, अति-यथार्थवादी जल प्रभाव, 8K UHD, फोटोरियलिस्टिक, वॉल्यूमेट्रिक लाइटिंग, धीमी कैमरा पैनिंग। पारंपरिक सोने के झुमके के साथ सजे हुए।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: पारंपरिक सोने के झुमके</li>
                                <li>समुद्र तट की प्राकृतिक सुंदरता</li>
                                <li>तटीय स्थिरता का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">10. शाही हवेली में ऐतिहासिक भव्यता</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">38 वर्षीय स्त्रैण पुरुष और 44 वर्षीय मांसल पुरुष शाही हवेली में खड़े हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">अति-यथार्थवादी, समृद्ध रंग, ऐतिहासिक सटीकता, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। छोटे मोती और क्रिस्टल पिरोए गए चोटी, एक चमकदार मोती कमल पुष्प हेयर क्लिप, एक केंद्रीय रूबी लटकन के साथ शानदार पारंपरिक सोने का चोकर हार, और एक केंद्रीय रूबी लटकन के साथ अलंकृत मांग टीका।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: व्यापक आभूषण संग्रह</li>
                                <li>शाही हवेली की भव्यता</li>
                                <li>ऐतिहासिक गरिमा का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- स्पष्ट रूप से कामुक/यौन विषय category -->
                <h3>स्पष्ट रूप से कामुक/यौन विषय</h3>

                <div class="prompt-card" data-category="explicit">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">1. कामुक इच्छा का आलिंगन</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष नग्न अवस्था में अंतरंगता से जुड़े हुए हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">क्लोज-अप शॉट, 8K UHD, DSLR 50mm लेंस, हाइपर-रियलिस्टिक, सिनेमैटिक लाइटिंग, फोटोरियलिस्टिक, वॉल्यूमेट्रिक लाइटिंग, धीमी ज़ूम-इन गति। कामुक इच्छा और गहरे प्रेम का चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>तकनीकी गुणवत्ता में सुधार</li>
                                <li>कामुक भावनाओं का सूक्ष्म चित्रण</li>
                                <li>कलात्मक प्रस्तुति पर जोर</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="explicit">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">2. रात्रि में गहन अंतरंगता</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">रात्रि में गहन अंतरंगता का दृश्य।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">नाइट शॉट, मूड लाइटिंग, 8K UHD, फोटोरियलिस्टिक, सिनेमैटिक लाइटिंग, धीमी कैमरा मूवमेंट। रात्रि की गहन अंतरंगता का कलात्मक चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>रात्रिकालीन मूड लाइटिंग</li>
                                <li>गहन भावनाओं का चित्रण</li>
                                <li>कलात्मक संवेदनशीलता</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="explicit">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">3. प्रेम की गहराई</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">प्रेम की गहराई का अभिव्यक्ति।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">इंटिमेट शॉट, सॉफ्ट लाइटिंग, 8K UHD, फोटोरियलिस्टिक, एमोशनल डेप्थ, धीमी कैमरा मूवमेंट। प्रेम की गहराई का भावनात्मक चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>भावनात्मक गहराई पर जोर</li>
                                <li>सॉफ्ट लाइटिंग का उपयोग</li>
                                <li>प्रेम की अभिव्यक्ति</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="explicit">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">4. भावनात्मक एकता</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">भावनात्मक एकता का दृश्य।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">क्लोज-अप शॉट, इमोशनल लाइटिंग, 8K UHD, फोटोरियलिस्टिक, सिनेमैटिक कंपोजिशन, धीमी ज़ूम। भावनात्मक एकता का गहरा चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>भावनात्मक एकता पर फोकस</li>
                                <li>सिनेमैटिक कंपोजिशन</li>
                                <li>गहरे संबंध का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="explicit">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">5. संवेदनशील क्षण</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">संवेदनशील और कोमल क्षण।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">सेंसिटिव शॉट, नेचुरल लाइटिंग, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट फोकस, धीमी कैमरा मूवमेंट। संवेदनशील क्षणों का कलात्मक चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>संवेदनशीलता पर जोर</li>
                                <li>प्राकृतिक प्रकाश का उपयोग</li>
                                <li>कोमल भावनाओं का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- भावनात्मक संकट और सांत्वना category -->
                <h3>भावनात्मक संकट और सांत्वना</h3>

                <div class="prompt-card" data-category="comfort">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">1. दुःख में सांत्वना</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">दुःख के समय में सांत्वना देने का दृश्य।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">इमोशनल शॉट, सॉफ्ट लाइटिंग, 8K UHD, फोटोरियलिस्टिक, कंपैशनेट कंपोजिशन, धीमी कैमरा मूवमेंट। दुःख में सांत्वना का भावनात्मक चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>करुणा और सांत्वना पर जोर</li>
                                <li>भावनात्मक समर्थन का चित्रण</li>
                                <li>सॉफ्ट लाइटिंग का उपयोग</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="comfort">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">2. चिंता में शांति</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">चिंता के समय में शांति प्रदान करने का दृश्य।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">कैल्मिंग शॉट, पीसफुल लाइटिंग, 8K UHD, फोटोरियलिस्टिक, सेरीन कंपोजिशन, धीमी कैमरा मूवमेंट। चिंता में शांति का चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>शांति और स्थिरता पर जोर</li>
                                <li>चिंता निवारण का चित्रण</li>
                                <li>शांतिपूर्ण वातावरण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="comfort">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">3. अकेलेपन में साथ</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">अकेलेपन के समय में साथ देने का दृश्य।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">कंपैनियनशिप शॉट, वार्म लाइटिंग, 8K UHD, फोटोरियलिस्टिक, सपोर्टिव कंपोजिशन, धीमी कैमरा मूवमेंट। अकेलेपन में साथ का चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>साथ और समर्थन पर जोर</li>
                                <li>अकेलेपन का निवारण</li>
                                <li>गर्म और सहायक वातावरण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="comfort">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">4. डर में हिम्मत</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">डर के समय में हिम्मत देने का दृश्य।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">करेजियस शॉट, एम्पावरिंग लाइटिंग, 8K UHD, फोटोरियलिस्टिक, स्ट्रेंथ कंपोजिशन, धीमी कैमरा मूवमेंट। डर में हिम्मत का चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>साहस और शक्ति पर जोर</li>
                                <li>डर का सामना करने की प्रेरणा</li>
                                <li>सशक्तिकरण का चित्रण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="comfort">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">5. निराशा में आशा</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">निराशा के समय में आशा जगाने का दृश्य।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">होपफुल शॉट, इंस्पायरिंग लाइटिंग, 8K UHD, फोटोरियलिस्टिक, अपलिफ्टिंग कंपोजिशन, धीमी कैमरा मूवमेंट। निराशा में आशा का चित्रण।</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आशा और प्रेरणा पर जोर</li>
                                <li>निराशा से उबरने का चित्रण</li>
                                <li>उत्साहजनक वातावरण</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        function togglePrompt(header) {
            const promptCard = header.parentElement;
            const content = promptCard.querySelector('.prompt-content');
            const icon = header.querySelector('.expand-icon');

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                promptCard.classList.remove('expanded');
            } else {
                content.classList.add('show');
                promptCard.classList.add('expanded');
            }
        }

        function searchPrompts(searchTerm) {
            const promptCards = document.querySelectorAll('.prompt-card');
            const searchLower = searchTerm.toLowerCase();

            promptCards.forEach(card => {
                const title = card.querySelector('.prompt-title').textContent.toLowerCase();
                const content = card.querySelector('.prompt-content').textContent.toLowerCase();

                if (title.includes(searchLower) || content.includes(searchLower)) {
                    card.style.display = 'block';
                    highlightText(card, searchTerm);
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function highlightText(element, searchTerm) {
            if (!searchTerm) {
                // Remove existing highlights
                const highlights = element.querySelectorAll('.highlight');
                highlights.forEach(highlight => {
                    const parent = highlight.parentNode;
                    parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                    parent.normalize();
                });
                return;
            }

            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            textNodes.forEach(textNode => {
                const text = textNode.textContent;
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                if (regex.test(text)) {
                    const highlightedText = text.replace(regex, '<span class="highlight">$1</span>');
                    const wrapper = document.createElement('div');
                    wrapper.innerHTML = highlightedText;

                    while (wrapper.firstChild) {
                        textNode.parentNode.insertBefore(wrapper.firstChild, textNode);
                    }
                    textNode.parentNode.removeChild(textNode);
                }
            });
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Set first tab as active
            showTab('overview');
        });
    </script>
</body>
</html>