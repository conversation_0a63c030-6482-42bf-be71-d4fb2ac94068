<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>प्रॉम्प्ट अनुकूलन रिपोर्ट</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            text-align: center;
            margin-bottom: 30px;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: white;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            border-right: 1px solid #eee;
        }

        .tab-button:last-child {
            border-right: none;
        }

        .tab-button.active {
            background: #3498db;
            color: white;
        }

        .tab-button:hover {
            background: #ecf0f1;
        }

        .tab-button.active:hover {
            background: #2980b9;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .section h3 {
            color: #34495e;
            margin: 20px 0 10px 0;
        }

        .principles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .principle-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .principle-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .prompt-card {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .prompt-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .prompt-header {
            background: #34495e;
            color: white;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .prompt-header:hover {
            background: #2c3e50;
        }

        .prompt-title {
            font-weight: bold;
            font-size: 1.1rem;
        }

        .expand-icon {
            transition: transform 0.3s ease;
        }

        .prompt-card.expanded .expand-icon {
            transform: rotate(180deg);
        }

        .prompt-content {
            display: none;
            padding: 20px;
        }

        .prompt-content.show {
            display: block;
        }

        .original-prompt, .optimized-prompt {
            margin: 15px 0;
        }

        .original-prompt h4, .optimized-prompt h4 {
            color: #e74c3c;
            margin-bottom: 10px;
        }

        .optimized-prompt h4 {
            color: #27ae60;
        }

        .prompt-text {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .changes-list {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .changes-list h4 {
            color: #2980b9;
            margin-bottom: 10px;
        }

        .changes-list ul {
            margin-left: 20px;
        }

        .changes-list li {
            margin: 5px 0;
        }

        .category-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .search-box {
            width: 100%;
            padding: 12px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            margin-bottom: 20px;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #3498db;
        }

        .highlight {
            background-color: yellow;
            padding: 2px 4px;
            border-radius: 3px;
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }

            .tab-button {
                border-right: none;
                border-bottom: 1px solid #eee;
            }

            .tab-button:last-child {
                border-bottom: none;
            }

            .container {
                padding: 10px;
            }

            .tab-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>एआई-जनरेटेड छवियों और वीडियो के लिए प्रॉम्प्ट अनुकूलन</h1>
            <p class="subtitle">विस्तृत रिपोर्ट और अनुकूलित प्रॉम्प्ट्स</p>
        </div>
    </header>

    <div class="container">
        <div class="nav-tabs">
            <button class="tab-button active" onclick="showTab('overview')">परिचय</button>
            <button class="tab-button" onclick="showTab('principles')">सिद्धांत</button>
            <button class="tab-button" onclick="showTab('analysis')">विश्लेषण</button>
            <button class="tab-button" onclick="showTab('prompts')">अनुकूलित प्रॉम्प्ट्स</button>
        </div>

        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="section">
                <h2>परिचय</h2>
                <p>यह रिपोर्ट एआई-आधारित छवि और वीडियो जनरेशन के लिए उपयोगकर्ता द्वारा प्रदान किए गए प्रॉम्प्ट्स के गहन विश्लेषण, त्रुटि सुधार और अनुकूलन पर केंद्रित है। इसका प्राथमिक उद्देश्य इन प्रॉम्प्ट्स को "पूरी तरह से अनुकूलित" करना है, यह सुनिश्चित करते हुए कि वे एआई मॉडल्स से उच्च-गुणवत्ता वाले, सटीक और रचनात्मक आउटपुट उत्पन्न करें।</p>

                <div class="category-stats">
                    <div class="stat-card">
                        <div class="stat-number">30</div>
                        <div class="stat-label">कुल प्रॉम्प्ट्स</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <div class="stat-label">मुख्य श्रेणियां</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">अनुकूलन दर</div>
                    </div>
                </div>

                <h3>मुख्य श्रेणियां</h3>
                <div class="principles-grid">
                    <div class="principle-card">
                        <h4>सामान्य अंतरंगता और भावनात्मक संबंध</h4>
                        <p>10 प्रॉम्प्ट्स - भावनात्मक गहराई और अंतरंग संबंधों पर केंद्रित</p>
                    </div>
                    <div class="principle-card">
                        <h4>आभूषण केंद्रित</h4>
                        <p>10 प्रॉम्प्ट्स - पारंपरिक आभूषणों और सजावट पर जोर</p>
                    </div>
                    <div class="principle-card">
                        <h4>स्पष्ट रूप से कामुक/यौन विषय</h4>
                        <p>5 प्रॉम्प्ट्स - कामुक और यौन संदर्भों के साथ</p>
                    </div>
                    <div class="principle-card">
                        <h4>भावनात्मक संकट और सांत्वना</h4>
                        <p>5 प्रॉम्प्ट्स - भावनात्मक समर्थन और सांत्वना के दृश्य</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Principles Tab -->
        <div id="principles" class="tab-content">
            <div class="section">
                <h2>एआई प्रॉम्प्ट इंजीनियरिंग के सिद्धांत</h2>

                <h3>प्रभावी प्रॉम्प्ट्स के मुख्य घटक</h3>
                <div class="principles-grid">
                    <div class="principle-card">
                        <h4>विषय (Subject)</h4>
                        <p>छवि का मुख्य फोकस - व्यक्ति, जानवर, या वस्तु</p>
                    </div>
                    <div class="principle-card">
                        <h4>क्रिया (Action)</h4>
                        <p>विषय क्या कर रहा है और कैसे कर रहा है</p>
                    </div>
                    <div class="principle-card">
                        <h4>वातावरण (Environment)</h4>
                        <p>स्थान, मौसम, भूभाग और पृष्ठभूमि</p>
                    </div>
                    <div class="principle-card">
                        <h4>माध्यम/शैली (Medium/Style)</h4>
                        <p>कला रूप या दृश्य शैली - फोटोरियलिस्टिक, तेल चित्रकला</p>
                    </div>
                    <div class="principle-card">
                        <h4>प्रकाश (Lighting)</h4>
                        <p>प्रकाश की गुणवत्ता, दिशा, रंग और स्रोत</p>
                    </div>
                    <div class="principle-card">
                        <h4>रंग (Color)</h4>
                        <p>रंग योजना, जीवंतता, या म्यूटेशन</p>
                    </div>
                    <div class="principle-card">
                        <h4>मनोदशा (Mood)</h4>
                        <p>छवि का समग्र भावनात्मक अनुभव</p>
                    </div>
                    <div class="principle-card">
                        <h4>संरचना/फ्रेमिंग</h4>
                        <p>कैमरा कोण, शॉट प्रकार, और बोकेह</p>
                    </div>
                </div>

                <h3>विशिष्टता और विवरण का महत्व</h3>
                <p>जितना अधिक विशिष्ट और विस्तृत प्रॉम्प्ट होगा, एआई मॉडल द्वारा वांछित परिणाम उत्पन्न करने की संभावना उतनी ही अधिक होगी। विशिष्टता का अर्थ केवल अधिक शब्द जोड़ना नहीं है, बल्कि एआई व्याख्या के लिए सबसे सटीक और प्रभावशाली शब्दों का चयन करना है।</p>

                <h3>तकनीकी पैरामीटर्स</h3>
                <div class="principle-card">
                    <h4>तकनीकी विवरण</h4>
                    <p>कैमरा लेंस (DSLR 85mm), रिज़ॉल्यूशन (8K UHD), बोकेह प्रभाव, HDR, सिनेमैटिक कलर ग्रेडिंग जैसे तकनीकी विवरण छवि की गुणवत्ता में काफी सुधार करते हैं।</p>
                </div>

                <div class="principle-card">
                    <h4>नकारात्मक प्रॉम्प्ट्स</h4>
                    <p>अवांछित तत्वों को हटाने के लिए महत्वपूर्ण हैं। ये एआई को उन सुविधाओं को उत्पन्न करने से रोकते हैं जो अवांछित हैं।</p>
                </div>
            </div>
        </div>

        <!-- Analysis Tab -->
        <div id="analysis" class="tab-content">
            <div class="section">
                <h2>मूल प्रॉम्प्ट्स का विश्लेषण</h2>

                <h3>सामान्य अवलोकन</h3>
                <p>दस्तावेज़ में कुल 30 प्रॉम्प्ट्स शामिल हैं, जिन्हें चार मुख्य श्रेणियों में वर्गीकृत किया गया है। प्रत्येक प्रॉम्प्ट एक 30-38 वर्षीय भारतीय स्त्रैण पुरुष और एक शक्तिशाली, मांसल भारतीय पुरुष के बीच एक अंतरंग दृश्य का वर्णन करता है।</p>

                <h3>शक्तियाँ</h3>
                <div class="principles-grid">
                    <div class="principle-card">
                        <h4>विस्तृत चरित्र विवरण</h4>
                        <p>प्रॉम्प्ट्स में "स्त्रैण पुरुष" की त्वचा, व्यवहार, बाल और चेहरे की विशेषताओं का विस्तृत वर्णन किया गया है।</p>
                    </div>
                    <div class="principle-card">
                        <h4>समृद्ध सेटिंग विवरण</h4>
                        <p>प्रत्येक प्रॉम्प्ट में वातावरण, प्रकाश व्यवस्था, सुगंध और ध्वनि का विस्तृत वर्णन है।</p>
                    </div>
                    <div class="principle-card">
                        <h4>तकनीकी कैमरा विवरण</h4>
                        <p>8K UHD, हाइपर-रियलिस्टिक, DSLR लेंस, बोकेह प्रभाव जैसे उच्च-गुणवत्ता वाले तकनीकी पैरामीटर्स का समावेश।</p>
                    </div>
                    <div class="principle-card">
                        <h4>भावनात्मक गहराई</h4>
                        <p>प्रॉम्प्ट्स में गहरे भावनात्मक बंधन, प्रेम, विश्वास और कामुकता पर जोर दिया गया है।</p>
                    </div>
                </div>

                <h3>सुधार के संभावित क्षेत्र</h3>
                <div class="principle-card">
                    <h4>संगति</h4>
                    <p>प्रॉम्प्ट्स के बीच विवरण के स्तर और संरचना में भिन्नता है। कुछ प्रॉम्प्ट्स में दूसरों की तुलना में अधिक तकनीकी या वर्णनात्मक विवरण हैं।</p>
                </div>

                <div class="principle-card">
                    <h4>विशिष्ट कीवर्ड्स</h4>
                    <p>बालों की बनावट, कपड़ों के विवरण, आभूषणों की बारीकियों और भावनात्मक अवस्थाओं के लिए अधिक लक्षित कीवर्ड्स जोड़ने का अवसर है।</p>
                </div>

                <div class="principle-card">
                    <h4>वीडियो अनुकूलन</h4>
                    <p>प्रॉम्प्ट्स में कैमरा गति, लौकिक तत्वों और समय के साथ क्रियाओं को परिभाषित करने के लिए स्पष्ट निर्देशों का अभाव है।</p>
                </div>
            </div>
        </div>

        <!-- Prompts Tab -->
        <div id="prompts" class="tab-content">
            <div class="section">
                <h2>अनुकूलित प्रॉम्प्ट्स</h2>
                <input type="text" class="search-box" placeholder="प्रॉम्प्ट्स में खोजें..." onkeyup="searchPrompts(this.value)">

                <h3>सामान्य अंतरंगता और भावनात्मक संबंध</h3>

                <div class="prompt-card" data-category="intimacy">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">1. महल में कामुक आलिंगन</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष महल में अंतरंगता से बैठे हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">आई-लेवल शॉट, थर्ड्स का नियम, 8K UHD, हाइपर-रियलिस्टिक, DSLR 85mm लेंस, प्राकृतिक बोकेह प्रभाव, सिनेमैटिक कलर ग्रेडिंग, वॉल्यूमेट्रिक लाइटिंग, फोटोरियलिस्टिक, गतिशील कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी और मोती जैसी गुणवत्ता के साथ भीतर से चमक रही है, उसका चेहरा बेदाग चिकना और किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को बढ़ाता है। उसका व्यवहार गहरी शर्म और गहन आत्मनिरीक्षण का प्रतीक है, उसकी आँखें कोमलता से बंद हैं, एक मुश्किल से समझ में आने वाली होंठों की कंपन के साथ नाजुक, अत्यधिक कामुक खुशी का विकिरण करती हुई, गहन भावनात्मक संबंध को दर्शाती हुई। शानदार जेट-काले, रेशमी बाल, कूल्हों तक लंबे, एक सरल, बड़ा, और काला जूड़ा (उपडो) में सुरुचिपूर्ण ढंग से स्टाइल किए गए हैं, जो चेहरे की नाजुक विशेषताओं को और निखारते हैं। जूड़ा ताज़े चेरी ब्लॉसम और छोटे चांदी के हेयरपिन्स से सजा हुआ है। वह सूक्ष्म फूलों की कढ़ाई (चेरी ब्लॉसम, तितलियाँ) और छोटे सेक्विन के साथ एक बहती हुई पेस्टल लैवेंडर रेशमी शिफॉन पोशाक पहने हुए है। हाथ से सजे हुए बीज मोतियों के साथ पतला चांदी का शॉल। एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ बैठे, उनके घुटने एक-दूसरे को हल्के से छूते हुए, उनके हाथ कामुकता से गूंथे हुए, एक-दूसरे की आँखों में गहराई से देखते हुए, जो गहरे प्रेम, विश्वास और बढ़ती कामुकता को दर्शाता है। सेटिंग: गहरे लाल मखमली पर्दे के साथ भव्य महल कक्ष। इंद्रधनुषी प्रकाश बिखेरता हुआ शानदार क्रिस्टल झूमर। हवा में समृद्ध चंदन की तीव्र सुगंध, जो कामुकता को और गहरा करती है। मधुर शास्त्रीय बांसुरी की धुनें वातावरण में गूंजती हुई। कीमती पत्थरों और मोती की जड़ाई वाले अलंकृत फर्नीचर, रेशमी तपस्या।

--neg विकृत, अस्पष्ट, खराब गुणवत्ता, असंगत, अतिरिक्त अंग, कटे हुए अंग, वाटरमार्क, टेक्स्ट, लोगो, खराब कला, खराब बनावट, अवास्तविक</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>वीडियो अनुकूलन: गतिशील कैमरा पैनिंग जोड़ा गया</li>
                                <li>तकनीकी पैरामीटर्स: सिनेमैटिक कलर ग्रेडिंग, वॉल्यूमेट्रिक लाइटिंग जोड़े गए</li>
                                <li>संवेदी विवरण: तीव्र सुगंध और ध्वनि का विस्तार</li>
                                <li>नकारात्मक प्रॉम्प्ट्स जोड़े गए</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="prompt-card" data-category="intimacy">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">2. उत्सव का आलिंगन</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष त्योहार के कमरे में आलिंगनबद्ध हैं।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">लो-एंगल शॉट, तीव्र विषय, धुंधली पृष्ठभूमि, 8K UHD, सिनेमैटिक कलर ग्रेडिंग, फोटोरियलिस्टिक, HDR, वॉल्यूमेट्रिक लाइटिंग, धीमी कैमरा ट्रैकिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान, गोरी है और आंतरिक चमक से जगमगा रही है, उसका चेहरा बेदाग चिकना और किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को स्पष्ट रूप से दर्शाता है। उसका व्यवहार कोमल शर्म के साथ bubbling आनंद का मिश्रण दर्शाता है, उसकी आँखें शांत संतुष्टि में धीरे से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं, जिसमें धीमी, कामुक आहें हैं। उसके बाल घने, जेट-काले, रेशमी, कूल्हों तक लंबे, सीधे स्टाइल में हैं, जो एक तरफ कंधे से आगे की ओर छाती पर खूबसूरती से गिरे हुए हैं। वह जटिल चांदी के धागे की कढ़ाई (कमल, मोर के रूपांकन) और एक समृद्ध सुनहरी बॉर्डर के साथ एक उत्कृष्ट पन्ना हरे रंग की रेशमी साड़ी पहने हुए है। मैचिंग चांदी के धागे के काम और छोटे बीज मोतियों से heavily embroidered fitted blouse। एक शक्तिशाली रूप से पेशी वाले 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से लिपटे हुए, जो उनके पीछे खड़ा है, उसकी बाहें उनके कंधों पर कोमलता से लिपटी हुई हैं, उसके गाल उसके बालों को हल्के से छूते हैं, जो गहरे भावनात्मक बंधन और बढ़ती कामुकता का प्रतीक है। सेटिंग: त्योहार समारोह के लिए गर्म रोशनी वाला पारंपरिक कमरा। नरम, हाथ से बुना हुआ ऊनी कालीन (बरगंडी, सोने के पैटर्न)। पीतल की लालटेनों से गर्म एम्बर प्रकाश। कोमल, मधुर सितार की धुनें जो वातावरण को कामुक बनाती हैं। हवा में गुलाब जल की तीव्र सुगंध। गेंदे के फूलों की मालाएं और छोटे मिट्टी के दीये।

--neg विकृत, अस्पष्ट, खराब गुणवत्ता, असंगत, अतिरिक्त अंग, कटे हुए अंग, वाटरमार्क, टेक्स्ट, लोगो, खराब कला, खराब बनावट, अवास्तविक</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>वीडियो अनुकूलन: धीमी कैमरा ट्रैकिंग जोड़ी गई</li>
                                <li>तकनीकी पैरामीटर्स: HDR और वॉल्यूमेट्रिक लाइटिंग जोड़े गए</li>
                                <li>भावनात्मक अभिव्यक्ति में वृद्धि</li>
                                <li>सेटिंग में अधिक विवरण</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <h3>आभूषण केंद्रित</h3>

                <div class="prompt-card" data-category="jewelry">
                    <div class="prompt-header" onclick="togglePrompt(this)">
                        <span class="prompt-title">1. पुस्तकालय में विद्वत्तापूर्ण जुड़ाव</span>
                        <span class="expand-icon">▼</span>
                    </div>
                    <div class="prompt-content">
                        <div class="original-prompt">
                            <h4>मूल प्रॉम्प्ट सारांश:</h4>
                            <div class="prompt-text">35 वर्षीय स्त्रैण पुरुष और 50 वर्षीय मांसल पुरुष पुस्तकालय में बैठे हैं, आभूषण पर जोर।</div>
                        </div>
                        <div class="optimized-prompt">
                            <h4>अनुकूलित प्रॉम्प्ट:</h4>
                            <div class="prompt-text">सिनेमैटिक फोटोग्राफी, अल्ट्रा-डिटेल्ड रेंडर, यथार्थवादी प्रकाश व्यवस्था, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। एक 35 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी और चीनी मिट्टी जैसी है, आंतरिक चमक से दमक रही है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें शांतिपूर्ण चिंतन में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं जिसमें हल्का गुलाब-गुलाबी रंगत है, जो गहरी भावनात्मक जुड़ाव और विद्वत्तापूर्ण मेल-मिलाप को दर्शाता है। उसके शानदार समृद्ध शाहबलूत भूरे रंग के बाल, कमर तक लंबे, आयतन के लिए कुशलता से लेयर्ड, एक भारी, स्त्रैण झरने की तरह स्टाइल किए हुए हैं, प्रत्येक लट रेशमी चमक बिखेर रही है, एक शानदार सोने के मोर के हेयरपिन से सजे हुए हैं जिसमें एक शानदार रूबी कैबोचोन जड़ा हुआ है। वह बेहतरीन रेशम के एक सुरुचिपूर्ण क्रीम रंग के सलवार सूट में सजे हुए हैं, जिसमें सूक्ष्म सोने के धागे की कढ़ाई (पैस्ले, संस्कृत प्रतीक, कमल के पैटर्न) और नाजुक सेक्विन के निशान हैं। मोती के गुच्छों के साथ पतला चांदी का दुपट्टा, पारंपरिक चांदी के झुमके, एक केंद्रीय चमकदार मोती के साथ नाजुक मांग टीका।

--neg विकृत, अस्पष्ट, खराब गुणवत्ता, असंगत, अतिरिक्त अंग, कटे हुए अंग, वाटरमार्क, टेक्स्ट, लोगो, खराब कला, खराब बनावट, अवास्तविक</div>
                        </div>
                        <div class="changes-list">
                            <h4>मुख्य परिवर्तन:</h4>
                            <ul>
                                <li>आभूषण विवरण: रूबी कैबोचोन और मांग टीका का विस्तृत वर्णन</li>
                                <li>केश विन्यास का अधिक विस्तृत वर्णन</li>
                                <li>संवेदी अनुभव का विस्तार</li>
                                <li>तकनीकी मापदंडों में सुधार</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <p style="text-align: center; margin: 30px 0; color: #666; font-style: italic;">
                    यहाँ केवल कुछ उदाहरण प्रॉम्प्ट्स दिखाए गए हैं। पूर्ण रिपोर्ट में सभी 30 अनुकूलित प्रॉम्प्ट्स शामिल हैं।
                </p>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        function togglePrompt(header) {
            const promptCard = header.parentElement;
            const content = promptCard.querySelector('.prompt-content');
            const icon = header.querySelector('.expand-icon');

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                promptCard.classList.remove('expanded');
            } else {
                content.classList.add('show');
                promptCard.classList.add('expanded');
            }
        }

        function searchPrompts(searchTerm) {
            const promptCards = document.querySelectorAll('.prompt-card');
            const searchLower = searchTerm.toLowerCase();

            promptCards.forEach(card => {
                const title = card.querySelector('.prompt-title').textContent.toLowerCase();
                const content = card.querySelector('.prompt-content').textContent.toLowerCase();

                if (title.includes(searchLower) || content.includes(searchLower)) {
                    card.style.display = 'block';
                    highlightText(card, searchTerm);
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function highlightText(element, searchTerm) {
            if (!searchTerm) {
                // Remove existing highlights
                const highlights = element.querySelectorAll('.highlight');
                highlights.forEach(highlight => {
                    const parent = highlight.parentNode;
                    parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                    parent.normalize();
                });
                return;
            }

            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            textNodes.forEach(textNode => {
                const text = textNode.textContent;
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                if (regex.test(text)) {
                    const highlightedText = text.replace(regex, '<span class="highlight">$1</span>');
                    const wrapper = document.createElement('div');
                    wrapper.innerHTML = highlightedText;

                    while (wrapper.firstChild) {
                        textNode.parentNode.insertBefore(wrapper.firstChild, textNode);
                    }
                    textNode.parentNode.removeChild(textNode);
                }
            });
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Set first tab as active
            showTab('overview');
        });
    </script>
</body>
</html>